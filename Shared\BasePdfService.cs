using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Companion;

namespace KPA.Services.QuestPDF.Shared;

public interface IBasePdfService<TRequest> where TRequest : PdfRequest
{
    string GenerateFileName(TRequest request);
    byte[] GeneratePdf(TRequest request);
    void ShowInCompanion(TRequest request);
}


/// <summary>
/// Base PDF service class with shared functionality
/// </summary>
/// <typeparam name="TRequest">The specific PDF request type</typeparam>
/// <typeparam name="TDocument">The specific PDF document type</typeparam>
public abstract class BasePdfService<TRequest, TDocument> : IBasePdfService<TRequest> where TRequest : PdfRequest
    where TDocument : BasePdfDocument<TRequest>
{
    protected readonly HttpClient _httpClient;
    protected readonly IConfiguration _configuration;


    static BasePdfService()
    {
        global::QuestPDF.Settings.License = LicenseType.Community;
    }

    protected BasePdfService(HttpClient httpClient, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _configuration = configuration;

    }

    /// <summary>
    /// Create a new document instance
    /// </summary>
    protected abstract TDocument CreateDocument(TRequest request);

    /// <summary>
    /// Generate PDF as byte array
    /// </summary>
    public byte[] GeneratePdf(TRequest request)
    {
        var document = CreateDocument(request);
        return document.GeneratePdf();
    }

    /// <summary>
    /// Show PDF in QuestPDF Companion app
    /// </summary>
    public void ShowInCompanion(TRequest request)
    {
        var document = CreateDocument(request);
        document.ShowInCompanion();
    }

    /// <summary>
    /// Generate filename for the PDF - can be overridden by derived classes
    /// </summary>
    public virtual string GenerateFileName(TRequest request)
    {
        if (!string.IsNullOrEmpty(request.CustomFileName))
            return request.CustomFileName;

        var sanitizedTitle = SanitizeFileName(GetTitleFromRequest(request));
        var year = DateTime.Now.Year.ToString();
        var suffix = GetFileNameSuffix();
        return $"{sanitizedTitle}_{year}_{suffix}.pdf";
    }

    /// <summary>
    /// Get the title from the request - must be implemented by derived classes
    /// </summary>
    protected abstract string GetTitleFromRequest(TRequest request);

    /// <summary>
    /// Get the file name suffix - must be implemented by derived classes
    /// </summary>
    protected abstract string GetFileNameSuffix();

    /// <summary>
    /// Get the default file name if title is empty - must be implemented by derived classes
    /// </summary>
    protected abstract string GetDefaultFileName();

    protected string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
        return string.IsNullOrEmpty(sanitized) ? GetDefaultFileName() : sanitized.Replace(" ", "_").ToLowerInvariant();
    }
}
