using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace KPA.Services.QuestPDF.Shared;

public interface IBasePdfDocument
{
    void Compose(IDocumentContainer container);
    DocumentMetadata GetMetadata();
    DocumentSettings GetSettings();
}


/// <summary>
/// Base PDF document class with shared functionality for image handling, caching, and A3 optimization
/// </summary>
/// <typeparam name="TRequest">The specific PDF request type</typeparam>
public abstract class BasePdfDocument<TRequest> : IDocument, IBasePdfDocument where TRequest : PdfRequest
{
    protected readonly TRequest _request;
    protected readonly HttpClient _httpClient;
    protected readonly Dictionary<string, byte[]> _imageCache = new();
    protected readonly Dictionary<string, ImageOrientation> _orientationCache = new();
    protected readonly string _brandColor = Colors.Grey.Darken4;

    protected BasePdfDocument(TRequest request, HttpClient httpClient, IConfiguration configuration)
    {
        _request = request;
        _httpClient = httpClient;
        _brandColor = configuration["QuestPDF:BrandColor"] ?? _brandColor;
        // Pre-download all images at initialization
        PreloadImages();
    }

    public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

    public virtual DocumentSettings GetSettings() => new DocumentSettings
    {
        ImageCompressionQuality = A3PaperConfig.DefaultCompression,
        ImageRasterDpi = A3PaperConfig.DefaultDPI, // Optimized for A3 paper size
        PdfA = false
    };

    /// <summary>
    /// Abstract method that each document type must implement for its specific layout
    /// </summary>
    public abstract void Compose(IDocumentContainer container);

    /// <summary>
    /// Abstract method to get image URLs from the specific request type
    /// </summary>
    protected abstract IEnumerable<string> GetImageUrls();

    #region Image Handling & Caching

    private void PreloadImages()
    {
        try
        {
            var imageUrls = GetImageUrls().Where(url => !string.IsNullOrEmpty(url)).ToList();


            foreach (var imageUrl in imageUrls)
            {
                try
                {
                    var imageBytes = DownloadImageAsync(imageUrl).GetAwaiter().GetResult();
                    if (imageBytes != null)
                    {
                        // Optimize image for A3 paper size
                        var optimizedBytes = OptimizeImageForA3(imageBytes);
                        _imageCache[imageUrl] = optimizedBytes;

                        // Also cache the orientation
                        var dimensions = GetImageDimensions(imageBytes);
                        Console.WriteLine($"Image dimensions for {imageUrl}: {dimensions.Width}x{dimensions.Height}");
                        if (dimensions.Width > 0 && dimensions.Height > 0)
                        {
                            if (dimensions.Width > dimensions.Height)
                            {
                                _orientationCache[imageUrl] = ImageOrientation.Landscape;
                            }
                            else if (dimensions.Height > dimensions.Width)
                            {
                                _orientationCache[imageUrl] = ImageOrientation.Portrait;
                            }
                            else
                            {
                                _orientationCache[imageUrl] = ImageOrientation.Square;
                            }
                        }
                        else
                        {
                            _orientationCache[imageUrl] = ImageOrientation.Landscape;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to preload image {imageUrl}: {ex.Message}");
                    // Continue with other images
                }
            }


            Console.WriteLine($"Preloaded {_imageCache.Count} images successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in PreloadImages: {ex.Message}");
        }
    }

    protected async Task<byte[]?> DownloadImageAsync(string imageUrl)
    {
        try
        {
            if (string.IsNullOrEmpty(imageUrl))
                return null;

            // Check if it's a web URL
            if (imageUrl.StartsWith("http://") || imageUrl.StartsWith("https://"))
            {
                var response = await _httpClient.GetAsync(imageUrl);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsByteArrayAsync();
                }
                else
                {
                    Console.WriteLine($"Failed to download image: {response.StatusCode} - {imageUrl}");
                    return null;
                }
            }
            else
            {
                // Local file path
                if (File.Exists(imageUrl))
                {
                    return await File.ReadAllBytesAsync(imageUrl);
                }
                else
                {
                    Console.WriteLine($"Local file not found: {imageUrl}");
                    return null;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error downloading image {imageUrl}: {ex.Message}");
            return null;
        }
    }

    protected ImageOrientation GetImageOrientation(string imageUrl)
    {
        // Use cached orientation if available
        if (_orientationCache.TryGetValue(imageUrl, out var cachedOrientation))
        {
            return cachedOrientation;
        }

        // Fallback to landscape if not in cache
        Console.WriteLine($"Image orientation not found in cache for: {imageUrl}");
        return ImageOrientation.Landscape;
    }

    protected byte[]? GetCachedImage(string imageUrl)
    {
        return _imageCache.TryGetValue(imageUrl, out var imageBytes) ? imageBytes : null;
    }

    #endregion

    #region Shared UI Components

    protected static void ComposeImageFallback(IContainer container, string message)
    {
        container
            .Padding(10)
            .Background(Colors.Grey.Lighten3)
            .AlignCenter()
            .AlignMiddle()
            .Text(message)
            .FontSize(16)
            .FontColor(Colors.Grey.Darken1);
    }

    protected void ComposeImage(IContainer container, string imageUrl, string fallbackMessage = "Image not available")
    {
        try
        {
            var imageBytes = GetCachedImage(imageUrl);


            if (imageBytes != null)
            {
                container
                    .Padding(10)
                    .Background(Colors.Purple.Lighten4)
                    .Image(imageBytes)
                    .FitArea();
            }
            else
            {
                ComposeImageFallback(container, fallbackMessage);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error composing image {imageUrl}: {ex.Message}");
            ComposeImageFallback(container, "Image failed to load");
        }
    }

    #endregion

    #region Image Analysis & Optimization

    protected (int Width, int Height) GetImageDimensions(byte[] imageBytes)
    {
        try
        {
            // Simple JPEG dimension detection
            if (imageBytes.Length > 10 && imageBytes[0] == 0xFF && imageBytes[1] == 0xD8)
            {
                return GetJpegDimensions(imageBytes);
            }

            // Simple PNG dimension detection

            if (imageBytes.Length > 24 &&

                imageBytes[0] == 0x89 && imageBytes[1] == 0x50 &&

                imageBytes[2] == 0x4E && imageBytes[3] == 0x47)
            {
                return GetPngDimensions(imageBytes);
            }

            // For other formats or if detection fails, assume landscape

            return (1920, 1080); // Default landscape dimensions
        }
        catch
        {
            return (1920, 1080); // Default landscape dimensions
        }
    }

    private static (int Width, int Height) GetJpegDimensions(byte[] imageBytes)
    {
        try
        {
            for (int i = 2; i < imageBytes.Length - 8; i++)
            {
                if (imageBytes[i] == 0xFF && (imageBytes[i + 1] == 0xC0 || imageBytes[i + 1] == 0xC2))
                {
                    int height = (imageBytes[i + 5] << 8) | imageBytes[i + 6];
                    int width = (imageBytes[i + 7] << 8) | imageBytes[i + 8];
                    return (width, height);
                }
            }
        }
        catch { }


        return (1920, 1080); // Default if parsing fails
    }

    private static (int Width, int Height) GetPngDimensions(byte[] imageBytes)
    {
        try
        {
            if (imageBytes.Length >= 24)
            {
                int width = (imageBytes[16] << 24) | (imageBytes[17] << 16) | (imageBytes[18] << 8) | imageBytes[19];
                int height = (imageBytes[20] << 24) | (imageBytes[21] << 16) | (imageBytes[22] << 8) | imageBytes[23];
                return (width, height);
            }
        }
        catch { }


        return (1920, 1080); // Default if parsing fails
    }

    private byte[] OptimizeImageForA3(byte[] imageBytes)
    {
        try
        {
            var dimensions = GetImageDimensions(imageBytes);

            // Use configuration for A3 optimization

            var maxWidth = A3PaperConfig.WIDTH_300DPI;   // High quality reference
            var maxHeight = A3PaperConfig.HEIGHT_300DPI;

            // Check if image is larger than optimal A3 size

            if (dimensions.Width > maxWidth || dimensions.Height > maxHeight)
            {
                Console.WriteLine($"Image {dimensions.Width}x{dimensions.Height} is larger than optimal A3 size, but keeping original for quality");
                // For now, keep original size but log the information
                // In the future, you could add actual image resizing here
            }
            else
            {
                Console.WriteLine($"Image {dimensions.Width}x{dimensions.Height} is optimal for A3 paper size");
            }

            // Return original bytes (compression is handled by QuestPDF settings)

            return imageBytes;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error optimizing image for A3: {ex.Message}");
            return imageBytes; // Return original if optimization fails
        }
    }

    #endregion
}

public enum ImageOrientation
{
    Portrait,
    Landscape,
    Square
}
