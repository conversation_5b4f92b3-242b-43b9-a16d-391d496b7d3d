using KPA.Services.QuestPDF.Services;

namespace KPA.Services.QuestPDF.WebAboutUs;

public static class WebAboutUsEndpoints
{
    public static void MapWebAboutUsEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/webaboutus/pdf")
            .WithTags("WebAboutUs PDF")
            .WithOpenApi();

        group.MapPost("/generate-and-upload", GenerateAndUploadAsync)
            .WithName("GenerateAndUploadWebAboutUsPdf")
            .WithSummary("Generate WebAboutUs PDF using shared architecture and upload to Azure Blob Storage");

        group.MapPost("/generate-and-preview", GenerateAndPreview)
            .WithName("GenerateAndPreviewWebAboutUsPdf")
            .WithSummary("Generate WebAboutUs PDF using shared architecture and open in QuestPDF Companion");

        group.MapPost("/download", Download)
            .WithName("DownloadWebAboutUsPdf")
            .WithSummary("Download WebAboutUs PDF directly");
    }

    private static async Task<IResult> GenerateAndUploadAsync(
        WebAboutUsPdfRequest request,
        WebAboutUsPdfService pdfService,
        IAzureBlobService azureBlobService,
        ILogger<Program> logger)
    {
        try
        {
            logger.LogInformation("Starting WebAboutUs PDF generation and upload");

            // Validate request
            if (string.IsNullOrEmpty(request.AzureConnectionString))
            {
                return Results.BadRequest(new PdfResponse
                {
                    Success = false,
                    ErrorMessage = "Azure connection string is required"
                });
            }

            // Generate PDF
            var pdfBytes = pdfService.GeneratePdf(request);
            logger.LogInformation("WebAboutUs PDF generated successfully, size: {Size} bytes", pdfBytes.Length);

            // Generate filename
            var fileName = pdfService.GenerateFileName(request);

            // Upload to Azure Blob Storage
            var pdfUrl = await azureBlobService.UploadPdfAsync(
                pdfBytes,
                fileName,
                request.ContainerName,
                request.AzureConnectionString);

            logger.LogInformation("WebAboutUs PDF uploaded successfully to: {PdfUrl}", pdfUrl);

            return Results.Ok(new PdfResponse
            {
                Success = true,
                PdfUrl = pdfUrl,
                FileName = fileName
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating or uploading WebAboutUs PDF");

            return Results.Problem(new PdfResponse
            {
                Success = false,
                ErrorMessage = $"An error occurred: {ex.Message}"
            }.ErrorMessage);
        }
    }

    private static IResult GenerateAndPreview(
        WebAboutUsPdfRequest request,
        WebAboutUsPdfService pdfService,
        ILogger<Program> logger)
    {
        try
        {
            logger.LogInformation("Starting WebAboutUs PDF generation and preview");

            // Generate and show in companion app
            pdfService.ShowInCompanion(request);
            logger.LogInformation("WebAboutUs PDF preview opened successfully");

            return Results.Ok(new PdfResponse
            {
                Success = true,
                FileName = pdfService.GenerateFileName(request)
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating WebAboutUs PDF preview");

            return Results.Problem(new PdfResponse
            {
                Success = false,
                ErrorMessage = $"An error occurred: {ex.Message}"
            }.ErrorMessage);
        }
    }

    private static IResult Download(
        WebAboutUsPdfRequest request,
        WebAboutUsPdfService pdfService,
        ILogger<Program> logger)
    {
        try
        {
            logger.LogInformation("Starting WebAboutUs PDF download");

            // Generate PDF
            var pdfBytes = pdfService.GeneratePdf(request);
            var fileName = pdfService.GenerateFileName(request);

            logger.LogInformation("WebAboutUs PDF generated for download, size: {Size} bytes", pdfBytes.Length);

            return Results.File(pdfBytes, "application/pdf", fileName);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating WebAboutUs PDF for download");
            return Results.Problem($"An error occurred: {ex.Message}");
        }
    }
}
