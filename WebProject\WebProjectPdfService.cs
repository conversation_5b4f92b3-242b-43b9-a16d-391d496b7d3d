using KPA.Services.QuestPDF.Shared;

namespace KPA.Services.QuestPDF.WebProject;

/// <summary>
/// WebProject PDF generator service using the shared base class
/// </summary>
public class WebProjectPdfService : BasePdfService<WebProjectPdfRequest, WebProjectPdfDocument>
{
    public WebProjectPdfService(HttpClient httpClient, IConfiguration configuration) : base(httpClient, configuration)
    {
    }

    protected override WebProjectPdfDocument CreateDocument(WebProjectPdfRequest request)
    {
        return new WebProjectPdfDocument(request, _httpClient, _configuration);
    }

    protected override string GetTitleFromRequest(WebProjectPdfRequest request)
    {
        return request.Title;
    }

    protected override string GetFileNameSuffix()
    {
        return "project";
    }

    protected override string GetDefaultFileName()
    {
        return "webproject";
    }
}
