using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using KPA.Services.QuestPDF.Shared;

namespace KPA.Services.QuestPDF.WebAboutUs;

/// <summary>
/// WebAboutUs PDF document using the shared base class
/// </summary>
public class WebAboutUsPdfDocument : BasePdfDocument<WebAboutUsPdfRequest>
{

    public WebAboutUsPdfDocument(WebAboutUsPdfRequest request, HttpClient httpClient, IConfiguration configuration)
        : base(request, httpClient, configuration)
    {
        Console.WriteLine($"BrandColor: {_brandColor}");
    }

    protected override IEnumerable<string> GetImageUrls()
    {
        return _request.Slides.Select(s => s.Url).Where(url => !string.IsNullOrEmpty(url));
    }

    public override void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                page.Size(PageSizes.A3.Landscape());
                page.Margin(20, Unit.Millimetre);
                page.DefaultTextStyle(x =>
                x.FontSize(16)
                .FontFamily(Fonts.Calibri)
                .FontColor(Colors.Grey.Darken4)
                );

                page.Content()
                .Column(mainColumn =>
                {
                    //3 vertical columns
                    mainColumn.Spacing(15);
                    mainColumn.Item().Element(ComposeAboutUs);
                    mainColumn.Item().Element(ComposeOurApproach);
                    mainColumn.Item().Element(ComposeOurServices);
                });
            });
    }

    private void ComposeAboutUs(IContainer container)
    {
        container
        .Extend()
            .AlignMiddle()
        // .BorderBottom(1).BorderColor(Colors.Grey.Lighten1)
        .Row(row =>
        {
            row.Spacing(15);
            row.RelativeItem(1).Column(column =>
            {
                // Title
                column.Item()
                .Padding(10)
                .Text("ABOUT US")
                    .FontSize(30)
                    // .AlignRight()
                    .Bold()
                    .FontColor(_brandColor);
            });
            row.RelativeItem(1).Column(column =>
            {
                // Description
                column.Item()
                .Padding(10)
                .Text(_request.AboutUs)
                .Justify();
            });
        });

    }

    private void ComposeOurApproach(IContainer container)
    {
        container
        .Extend()
        .AlignMiddle()
        // .BorderBottom(1).BorderColor(Colors.Grey.Lighten1)
        .Row(row =>
        {
            row.Spacing(15);
            row.RelativeItem(1).Column(column =>
            {
                // Title
                column.Item()
                 .Padding(10)
                .Text("OUR APPROACH")
                   .FontSize(30)
                    // .AlignRight()
                    .Bold()
                    .FontColor(_brandColor);
            });
            row.RelativeItem(1).Column(column =>
            {
                // Description
                column.Item()
                 .Padding(10)
                .Text(_request.OurApproach)
                .Justify();
            });
        });
    }

    private void ComposeOurServices(IContainer container)
    {

               container
        .Extend()
        .AlignMiddle()
        // .BorderBottom(1).BorderColor(Colors.Grey.Lighten1)
        .Row(row =>
        {
            row.Spacing(15);
            row.RelativeItem(1).Column(column =>
            {
                // Title
                column.Item()
                 .Padding(10)
                .Text("OUR SERVICES")
                   .FontSize(30)
                    // .AlignRight()
                    .Bold()
                    .FontColor(_brandColor);
            });
            row.RelativeItem(1).Column(column =>
            {
                column.Spacing(30);
                // Architecture
                column.Item()
                 .Padding(10)
                .Text("ARCHITECTURE")
                   .FontSize(28)
                    .Bold()
                    .FontColor(Colors.Grey.Darken3);
                // Image
                // column.Item().Element(container => ComposeImage(container, "ARCHITECTURE"));

                // Interiors
                column.Item()
                 .Padding(10)
                .Text("INTERIORS")
                   .FontSize(28)
                    .Bold()
                    .FontColor(Colors.Grey.Darken3);
                // Image
                // column.Item().Element(container => ComposeImage(container, "INTERIORS"));

                // Urban Design
                column.Item()
                 .Padding(10)
                .Text("URBAN DESIGN")
                   .FontSize(28)
                    .Bold()
                    .FontColor(Colors.Grey.Darken3);
                // Image
                // column.Item().Element(container => ComposeImage(container, "URBAN DESIGN"));
            });
        });
    }

    private void ComposeImage(IContainer container, string title)
    {
        Console.WriteLine($"ComposeImage: {title}");
         var validImage = _request.Slides.FirstOrDefault(s => s.Title == title);
         if (validImage == null)
         {
            Console.WriteLine($"No image found for {title}");
            return;
         }

        if (_imageCache.TryGetValue(validImage.Url, out var imageBytes))
        {
            container
                    .Height(200)
                        .Image(imageBytes)
                        .FitArea();
                }
        else
        {
            ComposeImageFallback(container, "Image not found in cache");
        }
    }

}
